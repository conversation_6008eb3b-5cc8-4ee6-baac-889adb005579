import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from '../../servicesV2/request';

// 奖励状态枚举
export enum AwardStatus {
  CLAIMING = 1,      // 领取中
  UNCLAIMED = 2,     // 未兑换
  CLAIMED = 5,       // 已兑换
  SHIPPED = 6,       // 已发货
  DELIVERED = 7,     // 已签收
  EXPIRED = 8,       // 已过期
}

// 奖励信息
export interface AwardInfo {
  orderNo: string;           // 订单号
  coverPic: string;          // 封面图片
  name: string;              // 奖品名称
  createTime: string;        // 创建时间
  expireTime: string;        // 过期时间
  awardCode: string;         // 奖品代码
  expressNo?: string;        // 快递单号
  expressLink?: string;      // 快递链接
  status: AwardStatus;       // 状态
}

// 查询转盘奖励记录响应
export interface QueryMyRotaryTableAwardResponse {
  success: boolean;
  failCode: number;
  hasMore: boolean;
  queryIndex: string;
  awardInfos: AwardInfo[];
}

/**
 * 查询我的转盘奖励记录
 * @returns 转盘奖励记录列表
 */
export const queryMyRotaryTableAward = async (): Promise<ResDataType<QueryMyRotaryTableAwardResponse> | undefined> => {
  return request<QueryMyRotaryTableAwardResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryMyRotaryTableAward/ts-${Date.now()}`,
    option: {
      method: 'get',
    }
  });
};

// 更新转盘奖品信息请求参数
export interface UpdateRotaryTableAwardInfoParams {
  orderNo: string;
  address: string;
  name: string;
  mobile: string;
}

// 更新转盘奖品信息响应
export interface UpdateRotaryTableAwardInfoResponse {
  success: boolean;
  failCode: number;
}

/**
 * 更新转盘奖品信息
 * @param params 奖品信息参数
 * @returns 更新结果
 */
export const updateRotaryTableAwardInfo = async (params: UpdateRotaryTableAwardInfoParams): Promise<ResDataType<UpdateRotaryTableAwardInfoResponse> | undefined> => {
  return request<UpdateRotaryTableAwardInfoResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/updateRotaryTableAwardInfo/ts-${Date.now()}`,
    option: {
      method: 'post',
      data: params,
    }
  });
};