import React, { useEffect, useContext, useState } from 'react';
import { View, Text, Image, FlatList, RefreshControl } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getStyles } from './styles';
import { ThemeContext } from '../../contextV2/themeContext';
import BackBtn from '../../componentsV2/common/BackBtn';
import reward_nodata from '../../appImagesV2/reward_nodata';
import reward_record_warning from '../../appImagesV2/reward_record_warning';
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter';
import { usePageReport } from '../../hooks/usePageReport';
import { RootStackParamList } from '../../router/type';
import { StackScreenProps } from '@react-navigation/stack';
import { RewardRecord as RewardRecordType } from './types';
import RewardRecordItem from './components/RewardRecordItem';
import { queryMyRotaryTableAward } from '../../services/welfare/rewardRecord';
import { Page } from '@xmly/rn-sdk/dist/Page';
import getUrlToOpen from '@xmly/rn-sdk/dist/Navigate';

export default function RewardRecord(props: StackScreenProps<RootStackParamList>) {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const theme = useContext(ThemeContext);
  const styles = getStyles(theme);

  // 使用简单的useState管理状态
  const [rewardList, setRewardList] = useState<RewardRecordType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 页面上报
  usePageReport({
    pageViewCode: 0, // 需要替换为实际的埋点代码
    pageExitCode: 0, // 需要替换为实际的埋点代码
    currPage: 'reward_record',
    params: {
      from: '实物奖品记录',
    },
    otherProps: props,
  });

  // 获取奖品记录数据
  const fetchRewardRecord = async () => {
    try {
      setLoading(true);
      const response = await queryMyRotaryTableAward();
      if (response?.data?.awardInfos) {
        // 直接使用接口返回的数据，不需要转换
        setRewardList(response.data.awardInfos);
      } else {
        setRewardList([]);
      }
    } catch (error) {
      console.error('获取奖品记录失败:', error);
      setRewardList([]);
    } finally {
      setLoading(false);
    }
  };

  // 下拉刷新
  const onRefresh = () => {
    fetchRewardRecord();
  };

  // 处理奖品记录点击
  const handleRecordPress = (item: RewardRecordType) => {
    if (item.status === 2) {
      // 状态为2时，跳转到信息填写页面
      navigation.navigate('PrizeInfoForm', {
        awardCode: item.awardCode,
        orderNo: item.orderNo
      });
    } else if (item.status === 6) {
      // 状态为6时，跳转到物流详情页面
      if (item.expressLink) {
        Page.start(getUrlToOpen(item.expressLink));
      }
    }
  };

  useEffect(() => {
    GlobalEventEmitter.emit('appContentReady');
    fetchRewardRecord();
  }, []);

  const paddingTop = 10 + insets.top;

  // 渲染列表项
  const renderItem = ({ item }: { item: RewardRecordType }) => (
    <RewardRecordItem item={item} onPress={handleRecordPress} />
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Image source={reward_nodata} style={styles.emptyImage} />
      <Text style={styles.emptyText}>空空如也</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { paddingTop }]}>
        <View style={[styles.backBtn, { top: paddingTop }]}>
          <BackBtn onPress={navigation.goBack} />
        </View>
        <Text style={styles.title}>实物奖品记录</Text>
      </View>

      {/* Warning Notice */}
      {rewardList.length > 0 && (
        <View style={styles.warningContainer}>
          <Image source={reward_record_warning} style={styles.warningIcon} />
          <Text style={styles.warningText}>
            本页面仅展示实物奖品记录，金币奖励请到收支记录查看
          </Text>
        </View>
      )}

      {/* Content */}
      {rewardList.length > 0 ? (
        <FlatList
          style={styles.listContainer}
          contentContainerStyle={styles.listContent}
          data={rewardList}
          renderItem={renderItem}
          keyExtractor={(item) => item.orderNo}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={onRefresh}
              tintColor={theme.common.title_color}
            />
          }
        />
      ) : loading ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>加载中...</Text>
        </View>
      ) : (
        renderEmptyState()
      )}
    </View>
  );
}
