import { StyleSheet } from 'react-native';
import { px } from '../../utils/px';
import { ThemeStyle } from '../../typesV2/themeInfo';

export const getStyles = (theme: ThemeStyle) => StyleSheet.create({
  // 最外层容器
  container: {
    flex: 1,
    backgroundColor: theme.container.bg_color,
  },
  
  // 头部区域
  header: {
    paddingTop: px(20),
    paddingBottom: px(10),
    zIndex: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: theme.common.bg_color,
  },
  backBtn: {
    position: 'absolute',
    left: 16,
  },
  title: {
    fontSize: px(17),
    lineHeight: px(24),
    color: theme.common.title_color,
  },
  
  // 警告提示区域
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.rewardRecord.warning_bg_color,
    paddingHorizontal: px(10),
    paddingVertical: px(10),
  },
  warningIcon: {
    width: px(15),
    height: px(15),
    marginRight: px(4),
  },
  warningText: {
    flex: 1,
    fontSize: px(12),
    color: theme.rewardRecord.warning_text_color,
  },
  
  // 列表区域
  listContainer: {
    flex: 1,
    paddingTop: px(16),
  },
  listContent: {
    paddingBottom: px(20),
  },
  
  // 空状态区域
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: px(40),
  },
  emptyImage: {
    width: px(200),
    height: px(108),
    marginBottom: px(20),
    resizeMode: 'contain',
  },
  emptyText: {
    fontSize: px(13),
    color: '#AAAAAA',
    textAlign: 'center',
  },
});
