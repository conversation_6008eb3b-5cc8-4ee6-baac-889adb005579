import React from 'react';
import { View, Text, Image, TouchableOpacity, Alert } from 'react-native';
import Clipboard from '@react-native-community/clipboard';
import { RewardRecord } from '../types';
import { getItemStyles } from './RewardRecordItem.styles';
import { useContext } from 'react';
import { ThemeContext } from '../../../contextV2/themeContext';
import reward_record_copy from '../../../appImagesV2/reward_record_copy';
import reward_record_clock from '../../../appImagesV2/reward_record_clock';
import reward_record_edit from '../../../appImagesV2/reward_record_edit';
import reward_record_overdue from '../../../appImagesV2/reward_record_overdue';
import { Toast } from '@xmly/rn-sdk';

interface RewardRecordItemProps {
  item: RewardRecord;
  onPress?: (item: RewardRecord) => void;
}

export default function RewardRecordItem({ item, onPress }: RewardRecordItemProps) {
  const theme = useContext(ThemeContext);
  const styles = getItemStyles(theme);

  const handlePress = () => {
    onPress?.(item);
  };

  // 复制兑换码
  const copyAwardCode = () => {
    Clipboard.setString(item.awardCode);
    Toast.info('兑换码已复制到剪贴板');
  };

  // 复制物流运单号
  const copyExpressNo = () => {
    if (item.expressNo) {
      Clipboard.setString(item.expressNo);
      Toast.info('物流运单号已复制到剪贴板');
    }
  };

  const getStatusIcon = (status: number) => {
    switch (status) {
      case 8: // 已过期
        return reward_record_overdue;
      default:
        return reward_record_clock;
    }
  };

  const formatExpireTime = (expireTime: string) => {
    try {
      const date = new Date(expireTime);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hour = date.getHours();
      return `${year}年${month}月${day}日${hour}点`;
    } catch (error) {
      return expireTime; // 如果解析失败，返回原始时间
    }
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 1:
        return '领取中';
      case 2:
        return '奖品未认领';
      case 5:
        return '奖品已认领，待发货';
      case 6:
        return '奖品已认领，物流中';
      case 7:
        return '奖品已签收';
      case 8:
        return '兑换已过期，未在规定日期内领取奖品';
      default:
        return '未知状态';
    }
  };

  const getButtonText = (status: number) => {
    switch (status) {
      case 2: // 未认领
        return '填写地址，领取奖品';
      case 6: // 物流中
        return '物流详情';
      default:
        return '';
    }
  };

  const shouldShowButton = (status: number) => {
    return status === 2 || status === 6;
  };

  return (
    <View style={styles.container}>
      {/* 商品图片 */}
      <Image source={{ uri: item.coverPic }} style={styles.productImage} />

      {/* 商品信息和按钮容器 */}
      <View style={styles.contentContainer}>
        {/* 商品信息 */}
        <View style={styles.productInfo}>
          <Text style={styles.productName} numberOfLines={1}>
            {item.name}
          </Text>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>中奖日期：</Text>
            <Text style={styles.infoValue}>{item.createTime}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>兑换码：</Text>
            <View style={styles.codeContainer}>
              <Text style={styles.infoValue}>{item.awardCode}</Text>
              <TouchableOpacity 
              style={styles.copyButton}
              onPress={copyAwardCode}>
                <Image source={reward_record_copy} style={styles.copyButtonImage} />
              </TouchableOpacity>
            </View>
          </View>

          {/* 物流运单号行 - 当status为6（已发货）或7（已签收）时显示 */}
          {(item.status === 6 || item.status === 7) && item.expressNo && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>物流运单号：</Text>
              <View style={styles.codeContainer}>
                <Text style={styles.infoValue}>{item.expressNo}</Text>
                <TouchableOpacity 
                style={styles.copyButton}
                onPress={copyExpressNo}>
                  <Image source={reward_record_copy} style={styles.copyButtonImage} />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* 分割线 */}
          <View style={styles.divider} />

          {/* 状态行 - 当status为2,5,6,7,8时显示 */}
          {(item.status === 2 || item.status === 5 || item.status === 6 || item.status === 7 || item.status === 8) && (
            <View style={styles.statusRow}>
              <Image source={getStatusIcon(item.status)} style={styles.statusIconImage} />
              <Text style={styles.statusText}>
                {getStatusText(item.status)}
                {item.status === 2 && '，'}
              </Text>
              {item.status === 2 && (
                <Text style={[styles.statusDetail, { color: '#ff4444' }]}>
                  兑换截止时间至{formatExpireTime(item.expireTime)}
                </Text>
              )}
            </View>
          )}
        </View>

        {/* 底部按钮 */}
        {shouldShowButton(item.status) && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handlePress}
            activeOpacity={1}
          >
            <Image source={reward_record_edit} style={styles.buttonIcon} />
            <Text style={styles.actionButtonText}>
              {getButtonText(item.status)}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}
